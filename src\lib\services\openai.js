import OpenAI from 'openai';
import { isReasoningModel, getModelConfig } from '../utils/modelUtils.js';

let openaiClient = null;

// 初始化OpenAI客户端（支持OpenRouter）
export function initializeOpenAI(apiKey, useOpenRouter = true) {
  if (!apiKey) {
    throw new Error('API key is required');
  }

  const config = {
    apiKey: apiKey,
    dangerouslyAllowBrowser: true
  };

  // 如果使用OpenRouter，设置自定义baseURL
  if (useOpenRouter) {
    config.baseURL = 'https://openrouter.ai/api/v1';
    config.defaultHeaders = {
      'HTTP-Referer': window.location.origin, // 可选：用于统计
      'X-Title': 'AI Chat Assistant' // 可选：应用名称
    };
  }

  openaiClient = new OpenAI(config);

  return openaiClient;
}

// 发送聊天消息
export async function sendChatMessage(messages, options = {}) {
  if (!openaiClient) {
    throw new Error('OpenAI client not initialized');
  }

  const {
    model = 'gpt-4o',
    temperature = 0.7,
    maxTokens = 2000,
    stream = false
  } = options;

  try {
    // 为思考模型调整参数
    const modelConfig = getModelConfig(model);
    const requestParams = {
      model,
      messages,
      temperature,
      max_tokens: maxTokens,
      stream
    };

    // 思考模型特殊处理
    if (modelConfig.isReasoning) {
      // 思考模型通常需要更多token和时间
      requestParams.max_tokens = Math.max(maxTokens, modelConfig.recommendedMaxTokens);

      // 某些思考模型不支持system prompt，需要转换
      if (!modelConfig.supportsSystemPrompt) {
        requestParams.messages = convertSystemPromptForReasoningModel(messages);
      }

      // 添加思考模型特定的参数
      if (model.includes('o1') || model.includes('o3')) {
        // OpenAI o1/o3 系列不支持temperature和system prompt
        delete requestParams.temperature;
        requestParams.messages = requestParams.messages.filter(msg => msg.role !== 'system');

        // 将system prompt合并到第一个user消息中
        const systemMsg = messages.find(msg => msg.role === 'system');
        if (systemMsg && requestParams.messages.length > 0 && requestParams.messages[0].role === 'user') {
          requestParams.messages[0].content = `${systemMsg.content}\n\n${requestParams.messages[0].content}`;
        }
      }
    }

    const response = await openaiClient.chat.completions.create(requestParams);

    return response;
  } catch (error) {
    console.error('OpenAI API error:', error);
    throw new Error(`OpenAI API error: ${error.message}`);
  }
}

// 为思考模型转换system prompt
function convertSystemPromptForReasoningModel(messages) {
  const systemMsg = messages.find(msg => msg.role === 'system');
  const otherMessages = messages.filter(msg => msg.role !== 'system');

  if (!systemMsg || otherMessages.length === 0) {
    return messages;
  }

  // 将system prompt添加到第一个user消息前
  const firstUserIndex = otherMessages.findIndex(msg => msg.role === 'user');
  if (firstUserIndex !== -1) {
    otherMessages[firstUserIndex] = {
      ...otherMessages[firstUserIndex],
      content: `${systemMsg.content}\n\n${otherMessages[firstUserIndex].content}`
    };
  }

  return otherMessages;
}

// 流式聊天消息
export async function* streamChatMessage(messages, options = {}) {
  if (!openaiClient) {
    throw new Error('OpenAI client not initialized');
  }

  const {
    model = 'gpt-4o',
    temperature = 0.7,
    maxTokens = 2000
  } = options;

  try {
    // 为思考模型调整参数
    const modelConfig = getModelConfig(model);
    const requestParams = {
      model,
      messages,
      temperature,
      max_tokens: maxTokens,
      stream: true
    };

    // 思考模型特殊处理
    if (modelConfig.isReasoning) {
      requestParams.max_tokens = Math.max(maxTokens, modelConfig.recommendedMaxTokens);

      if (model.includes('o1') || model.includes('o3')) {
        // OpenAI o1/o3 系列特殊处理
        delete requestParams.temperature;
        requestParams.messages = requestParams.messages.filter(msg => msg.role !== 'system');

        const systemMsg = messages.find(msg => msg.role === 'system');
        if (systemMsg && requestParams.messages.length > 0 && requestParams.messages[0].role === 'user') {
          requestParams.messages[0].content = `${systemMsg.content}\n\n${requestParams.messages[0].content}`;
        }
      }
    }

    const stream = await openaiClient.chat.completions.create(requestParams);

    let isThinkingPhase = false;
    let thinkingContent = '';

    for await (const chunk of stream) {
      const content = chunk.choices[0]?.delta?.content;
      if (content) {
        // 检测思考模型的思考阶段
        if (modelConfig.isReasoning) {
          // 检测思考开始标记
          if (content.includes('<thinking>') || content.includes('[思考]')) {
            isThinkingPhase = true;
            thinkingContent += content;
            yield `🧠 思考中... ${content}`;
            continue;
          }

          // 检测思考结束标记
          if (content.includes('</thinking>') || content.includes('[/思考]')) {
            isThinkingPhase = false;
            thinkingContent += content;
            yield `\n\n--- 思考完成 ---\n\n`;
            continue;
          }

          // 如果在思考阶段，显示思考内容
          if (isThinkingPhase) {
            thinkingContent += content;
            yield content;
            continue;
          }
        }

        // 正常内容输出
        yield content;
      }
    }
  } catch (error) {
    console.error('OpenAI streaming error:', error);
    throw new Error(`OpenAI streaming error: ${error.message}`);
  }
}

// 分析图片
export async function analyzeImage(imageUrl, prompt = "What's in this image?", options = {}) {
  if (!openaiClient) {
    throw new Error('OpenAI client not initialized');
  }

  const {
    model = 'gpt-4o',
    maxTokens = 1000
  } = options;

  try {
    const response = await openaiClient.chat.completions.create({
      model,
      messages: [
        {
          role: "user",
          content: [
            { type: "text", text: prompt },
            {
              type: "image_url",
              image_url: {
                url: imageUrl,
                detail: "high"
              }
            }
          ]
        }
      ],
      max_tokens: maxTokens
    });

    return response.choices[0].message.content;
  } catch (error) {
    console.error('Image analysis error:', error);
    throw new Error(`Image analysis error: ${error.message}`);
  }
}

// 获取可用模型列表
export async function getAvailableModels() {
  if (!openaiClient) {
    throw new Error('OpenAI client not initialized');
  }

  try {
    const response = await openaiClient.models.list();
    return response.data
      .filter(model => model.id.includes('gpt'))
      .sort((a, b) => a.id.localeCompare(b.id));
  } catch (error) {
    console.error('Failed to fetch models:', error);
    // 返回OpenRouter支持的热门模型列表
    return [
      { id: 'openai/gpt-4o', object: 'model' },
      { id: 'openai/gpt-4o-mini', object: 'model' },
      { id: 'openai/gpt-4-turbo', object: 'model' },
      { id: 'openai/gpt-3.5-turbo', object: 'model' },
      { id: 'anthropic/claude-3.5-sonnet', object: 'model' },
      { id: 'anthropic/claude-3-haiku', object: 'model' },
      { id: 'google/gemini-pro-1.5', object: 'model' },
      { id: 'meta-llama/llama-3.1-70b-instruct', object: 'model' },
      { id: 'mistralai/mistral-7b-instruct', object: 'model' },
      { id: 'qwen/qwen-2-72b-instruct', object: 'model' }
    ];
  }
}
