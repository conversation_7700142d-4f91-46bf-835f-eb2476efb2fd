import OpenAI from 'openai';

let openaiClient = null;

// 初始化OpenAI客户端（支持OpenRouter）
export function initializeOpenAI(apiKey, useOpenRouter = true) {
  if (!apiKey) {
    throw new Error('API key is required');
  }

  const config = {
    apiKey: apiKey,
    dangerouslyAllowBrowser: true
  };

  // 如果使用OpenRouter，设置自定义baseURL
  if (useOpenRouter) {
    config.baseURL = 'https://openrouter.ai/api/v1';
    config.defaultHeaders = {
      'HTTP-Referer': window.location.origin, // 可选：用于统计
      'X-Title': 'AI Chat Assistant' // 可选：应用名称
    };
  }

  openaiClient = new OpenAI(config);

  return openaiClient;
}

// 发送聊天消息
export async function sendChatMessage(messages, options = {}) {
  if (!openaiClient) {
    throw new Error('OpenAI client not initialized');
  }

  const {
    model = 'gpt-4o',
    temperature = 0.7,
    maxTokens = 2000,
    stream = false
  } = options;

  try {
    const response = await openaiClient.chat.completions.create({
      model,
      messages,
      temperature,
      max_tokens: maxTokens,
      stream
    });

    return response;
  } catch (error) {
    console.error('OpenAI API error:', error);
    throw new Error(`OpenAI API error: ${error.message}`);
  }
}

// 流式聊天消息
export async function* streamChatMessage(messages, options = {}) {
  if (!openaiClient) {
    throw new Error('OpenAI client not initialized');
  }

  const {
    model = 'gpt-4o',
    temperature = 0.7,
    maxTokens = 2000
  } = options;

  try {
    const stream = await openaiClient.chat.completions.create({
      model,
      messages,
      temperature,
      max_tokens: maxTokens,
      stream: true
    });

    for await (const chunk of stream) {
      const content = chunk.choices[0]?.delta?.content;
      if (content) {
        yield content;
      }
    }
  } catch (error) {
    console.error('OpenAI streaming error:', error);
    throw new Error(`OpenAI streaming error: ${error.message}`);
  }
}

// 分析图片
export async function analyzeImage(imageUrl, prompt = "What's in this image?", options = {}) {
  if (!openaiClient) {
    throw new Error('OpenAI client not initialized');
  }

  const {
    model = 'gpt-4o',
    maxTokens = 1000
  } = options;

  try {
    const response = await openaiClient.chat.completions.create({
      model,
      messages: [
        {
          role: "user",
          content: [
            { type: "text", text: prompt },
            {
              type: "image_url",
              image_url: {
                url: imageUrl,
                detail: "high"
              }
            }
          ]
        }
      ],
      max_tokens: maxTokens
    });

    return response.choices[0].message.content;
  } catch (error) {
    console.error('Image analysis error:', error);
    throw new Error(`Image analysis error: ${error.message}`);
  }
}

// 获取可用模型列表
export async function getAvailableModels() {
  if (!openaiClient) {
    throw new Error('OpenAI client not initialized');
  }

  try {
    const response = await openaiClient.models.list();
    return response.data
      .filter(model => model.id.includes('gpt'))
      .sort((a, b) => a.id.localeCompare(b.id));
  } catch (error) {
    console.error('Failed to fetch models:', error);
    // 返回OpenRouter支持的热门模型列表
    return [
      { id: 'openai/gpt-4o', object: 'model' },
      { id: 'openai/gpt-4o-mini', object: 'model' },
      { id: 'openai/gpt-4-turbo', object: 'model' },
      { id: 'openai/gpt-3.5-turbo', object: 'model' },
      { id: 'anthropic/claude-3.5-sonnet', object: 'model' },
      { id: 'anthropic/claude-3-haiku', object: 'model' },
      { id: 'google/gemini-pro-1.5', object: 'model' },
      { id: 'meta-llama/llama-3.1-70b-instruct', object: 'model' },
      { id: 'mistralai/mistral-7b-instruct', object: 'model' },
      { id: 'qwen/qwen-2-72b-instruct', object: 'model' }
    ];
  }
}
