{"name": "aaa", "version": "0.1.0", "description": "", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "check": "svelte-kit sync && svelte-check --tsconfig ./jsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./jsconfig.json --watch", "tauri": "tauri"}, "license": "MIT", "dependencies": {"@tailwindcss/postcss": "^4.1.11", "@tailwindcss/typography": "^0.5.16", "@tauri-apps/api": "^2", "@tauri-apps/plugin-opener": "^2", "autoprefixer": "^10.4.21", "lucide-svelte": "^0.525.0", "openai": "^5.10.1", "pdf-parse": "^1.1.1", "postcss": "^8.5.6", "tailwindcss": "^4.1.11"}, "devDependencies": {"@sveltejs/adapter-static": "^3.0.6", "@sveltejs/kit": "^2.9.0", "@sveltejs/vite-plugin-svelte": "^5.0.0", "@tauri-apps/cli": "^2", "svelte": "^5.0.0", "svelte-check": "^4.0.0", "typescript": "~5.6.2", "vite": "^6.0.3"}}