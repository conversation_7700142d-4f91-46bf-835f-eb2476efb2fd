import { writable, derived, get } from 'svelte/store';
import { browser } from '$app/environment';

// 消息类型
export const MESSAGE_TYPES = {
  TEXT: 'text',
  IMAGE: 'image',
  FILE: 'file',
  SYSTEM: 'system'
};

// 消息角色
export const MESSAGE_ROLES = {
  USER: 'user',
  ASSISTANT: 'assistant',
  SYSTEM: 'system'
};

// 创建唯一ID
function generateId() {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

// 创建消息对象
export function createMessage(content, role = MESSAGE_ROLES.USER, type = MESSAGE_TYPES.TEXT, metadata = {}) {
  return {
    id: generateId(),
    content,
    role,
    type,
    timestamp: new Date().toISOString(),
    metadata
  };
}

// 当前会话ID
export const currentSessionId = writable(generateId());

// 所有会话
export const sessions = writable({});

// 当前会话的消息
export const currentMessages = writable([]);

// 加载状态
export const isLoading = writable(false);

// 错误状态
export const error = writable(null);

// 从localStorage加载会话数据
function loadSessions() {
  if (browser) {
    try {
      const saved = localStorage.getItem('ai-chat-sessions');
      if (saved) {
        return JSON.parse(saved);
      }
    } catch (error) {
      console.error('Failed to load sessions:', error);
    }
  }
  return {};
}

// 保存会话到localStorage
function saveSessions(sessionsData) {
  if (browser) {
    try {
      localStorage.setItem('ai-chat-sessions', JSON.stringify(sessionsData));
    } catch (error) {
      console.error('Failed to save sessions:', error);
    }
  }
}

// 初始化会话数据
sessions.set(loadSessions());

// 当前会话的消息（派生store）
export const messages = derived(
  [sessions, currentSessionId],
  ([$sessions, $currentSessionId]) => {
    return $sessions[$currentSessionId]?.messages || [];
  }
);

// 添加消息到当前会话
export function addMessage(content, role = MESSAGE_ROLES.USER, type = MESSAGE_TYPES.TEXT, metadata = {}) {
  const message = createMessage(content, role, type, metadata);
  
  sessions.update(allSessions => {
    const sessionId = get(currentSessionId);
    
    if (!allSessions[sessionId]) {
      allSessions[sessionId] = {
        id: sessionId,
        title: generateSessionTitle(content),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        messages: []
      };
    }
    
    allSessions[sessionId].messages.push(message);
    allSessions[sessionId].updatedAt = new Date().toISOString();
    
    saveSessions(allSessions);
    return allSessions;
  });
  
  return message;
}

// 更新消息内容（用于流式响应）
export function updateMessage(messageId, content) {
  sessions.update(allSessions => {
    const sessionId = get(currentSessionId);
    const session = allSessions[sessionId];
    
    if (session) {
      const messageIndex = session.messages.findIndex(m => m.id === messageId);
      if (messageIndex !== -1) {
        session.messages[messageIndex].content = content;
        session.updatedAt = new Date().toISOString();
        saveSessions(allSessions);
      }
    }
    
    return allSessions;
  });
}

// 删除消息
export function deleteMessage(messageId) {
  sessions.update(allSessions => {
    const sessionId = get(currentSessionId);
    const session = allSessions[sessionId];
    
    if (session) {
      session.messages = session.messages.filter(m => m.id !== messageId);
      session.updatedAt = new Date().toISOString();
      saveSessions(allSessions);
    }
    
    return allSessions;
  });
}

// 创建新会话
export function createNewSession() {
  const newSessionId = generateId();
  currentSessionId.set(newSessionId);
  
  sessions.update(allSessions => {
    allSessions[newSessionId] = {
      id: newSessionId,
      title: 'New Chat',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      messages: []
    };
    
    saveSessions(allSessions);
    return allSessions;
  });
  
  return newSessionId;
}

// 切换会话
export function switchSession(sessionId) {
  currentSessionId.set(sessionId);
}

// 删除会话
export function deleteSession(sessionId) {
  sessions.update(allSessions => {
    delete allSessions[sessionId];
    saveSessions(allSessions);
    
    // 如果删除的是当前会话，创建新会话
    if (get(currentSessionId) === sessionId) {
      createNewSession();
    }
    
    return allSessions;
  });
}

// 重命名会话
export function renameSession(sessionId, newTitle) {
  sessions.update(allSessions => {
    if (allSessions[sessionId]) {
      allSessions[sessionId].title = newTitle;
      allSessions[sessionId].updatedAt = new Date().toISOString();
      saveSessions(allSessions);
    }
    
    return allSessions;
  });
}

// 生成会话标题
function generateSessionTitle(firstMessage) {
  if (typeof firstMessage === 'string') {
    return firstMessage.length > 30 
      ? firstMessage.substring(0, 30) + '...'
      : firstMessage;
  }
  return 'New Chat';
}

// 清空所有会话
export function clearAllSessions() {
  sessions.set({});
  saveSessions({});
  createNewSession();
}

// 导出会话数据
export function exportSessions() {
  const allSessions = get(sessions);
  const dataStr = JSON.stringify(allSessions, null, 2);
  const dataBlob = new Blob([dataStr], { type: 'application/json' });
  
  const link = document.createElement('a');
  link.href = URL.createObjectURL(dataBlob);
  link.download = `ai-chat-sessions-${new Date().toISOString().split('T')[0]}.json`;
  link.click();
}

// 导入会话数据
export function importSessions(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    
    reader.onload = (event) => {
      try {
        const importedSessions = JSON.parse(event.target.result);
        sessions.set(importedSessions);
        saveSessions(importedSessions);
        resolve(importedSessions);
      } catch (error) {
        reject(new Error('Invalid session file format'));
      }
    };
    
    reader.onerror = () => {
      reject(new Error('Failed to read file'));
    };
    
    reader.readAsText(file);
  });
}


