# 自定义模型使用指南

本应用现在支持自定义模型名称输入，并为思考模型（Reasoning Models）提供了特殊支持。

## 🚀 新功能特性

### 1. 自定义模型输入
- **快速选择**: 从预设的热门模型中快速选择
- **自定义输入**: 手动输入任何OpenRouter支持的模型名称
- **智能提示**: 自动显示模型信息和推荐配置
- **一键应用**: 快速应用推荐的参数设置

### 2. 思考模型特殊支持
- **自动检测**: 自动识别思考模型（o1、o3、Gemini Thinking等）
- **思考过程显示**: 可展开查看AI的推理过程
- **参数优化**: 自动调整温度、token等参数
- **特殊处理**: 针对不同思考模型的API兼容性处理

## 🧠 支持的思考模型

### OpenAI 思考模型
- `openai/o3-mini` - 最新的o3-mini推理模型
- `openai/o1` - GPT-o1推理模型
- `openai/o1-mini` - GPT-o1 Mini版本
- `openai/o1-preview` - GPT-o1预览版

### Google 思考模型
- `google/gemini-2.0-flash-thinking-exp` - Gemini 2.0 Flash Thinking
- `google/gemini-2.5-pro-exp` - Gemini 2.5 Pro实验版

### DeepSeek 思考模型
- `deepseek/deepseek-r1` - DeepSeek R1推理模型
- `deepseek/deepseek-r1-distill-llama-70b` - DeepSeek R1蒸馏版

### 其他思考模型
- `qwen/qwen-2.5-72b-instruct:thinking` - Qwen思考版本

## 📝 使用方法

### 1. 快速选择模型
1. 打开设置页面
2. 在"Model Selection"部分找到"Quick Select"下拉菜单
3. 选择一个预设模型，会自动填入到自定义输入框

### 2. 自定义输入模型
1. 在"Custom Model Name"输入框中直接输入模型名称
2. 格式：`provider/model-name`
3. 例如：`anthropic/claude-3.5-sonnet`、`openai/gpt-4o`

### 3. 查看模型信息
- 输入模型名称后，下方会自动显示：
  - 模型显示名称和类型标签
  - 推荐的温度和token设置
  - 模型特性说明
  - 思考模型的特殊提示

### 4. 应用推荐设置
- 点击"应用推荐设置"按钮
- 自动设置最适合该模型的参数

## 🔧 思考模型特殊功能

### 思考过程显示
- 思考模型的回复会自动检测思考内容
- 思考过程会显示在可展开的区域中
- 点击"🧠 思考过程"可查看AI的推理步骤

### 自动参数调整
思考模型会自动应用以下优化：
- **温度**: 通常设为1.0（更高的创造性）
- **Max Tokens**: 增加到8000+（支持长推理）
- **System Prompt**: 特殊处理（某些模型不支持）
- **流式响应**: 优化显示思考过程

### API兼容性处理
- **OpenAI o1/o3系列**: 自动移除temperature参数，转换system prompt
- **Gemini Thinking**: 保持完整参数支持
- **DeepSeek R1**: 优化推理token分配

## 📋 模型分类说明

### 🧠 Reasoning Models (推理模型)
- 专门用于复杂推理任务
- 会显示详细的思考过程
- 响应时间较长但质量更高
- 适合数学、逻辑、编程等任务

### ⚡ Fast Models (快速模型)
- 响应速度快
- 适合日常对话
- 成本相对较低
- 平衡性能和效率

### 🔬 Experimental (实验模型)
- 最新的实验性功能
- 可能不稳定
- 通常有独特能力
- 适合尝试新功能

## 💡 使用建议

### 选择思考模型的场景
- 复杂的数学计算
- 逻辑推理问题
- 代码调试和优化
- 需要详细分析的任务
- 多步骤问题解决

### 选择快速模型的场景
- 日常聊天对话
- 简单的问答
- 文本生成和编辑
- 快速原型开发
- 成本敏感的应用

### 参数调整建议
- **思考模型**: 温度1.0，token 8000+
- **创作模型**: 温度0.8-1.2，token 2000-4000
- **分析模型**: 温度0.3-0.7，token 1000-3000
- **对话模型**: 温度0.7，token 1000-2000

## 🔍 故障排除

### 模型不可用
- 检查模型名称拼写
- 确认OpenRouter支持该模型
- 查看账户余额和权限
- 尝试其他类似模型

### 思考过程不显示
- 确认使用的是思考模型
- 检查模型是否正确返回思考标记
- 尝试刷新页面重新开始对话

### 参数设置无效
- 某些模型不支持特定参数
- 思考模型可能忽略temperature设置
- 检查控制台错误信息

## 📚 更多资源

- [OpenRouter模型列表](https://openrouter.ai/models)
- [思考模型对比](https://openrouter.ai/models?q=reasoning)
- [API文档](https://openrouter.ai/docs)
- [定价信息](https://openrouter.ai/models)

## 🆕 最新更新

- ✅ 支持自定义模型名称输入
- ✅ 思考模型自动检测和优化
- ✅ 思考过程可视化显示
- ✅ 智能参数推荐
- ✅ 模型信息实时显示
- ✅ 一键应用推荐设置

现在您可以轻松使用任何OpenRouter支持的模型，并享受思考模型的强大推理能力！
