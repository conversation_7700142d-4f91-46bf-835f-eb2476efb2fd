# AI Chat Assistant

一个功能完整的AI对话应用，基于Tauri + SvelteKit构建，支持OpenAI API、PDF和图片读取，具有美观的现代化界面。

## 功能特性

### 🤖 AI对话
- 支持OpenAI GPT-4o、GPT-4o-mini、GPT-4-turbo等多种模型
- 流式响应，实时显示AI回复
- 可自定义系统提示词、温度、最大token数等参数
- 支持对话历史记录和会话管理

### 📁 文件处理
- **PDF文档**: 自动提取文本内容，支持与AI讨论PDF内容
- **图片分析**: 支持JPG、PNG、GIF、WebP格式，AI可分析图片内容
- **文本文件**: 支持TXT、Markdown、CSV等格式
- 拖拽上传，支持多文件同时处理

### 💬 会话管理
- 创建、重命名、删除会话
- 会话历史自动保存到本地存储
- 导出/导入会话数据
- 智能会话标题生成

### 🎨 界面设计
- 现代化的Material Design风格界面
- 支持亮色/暗色主题切换
- 响应式设计，适配不同屏幕尺寸
- 流畅的动画效果和交互体验

### ⚙️ 设置配置
- OpenAI API密钥配置
- 模型选择和参数调整
- 系统提示词自定义
- 主题和界面设置

## 快速开始

### 1. 安装依赖
```bash
npm install
```

### 2. 配置API密钥
1. 启动应用后，点击设置按钮
2. 输入您的OpenAI API密钥（从 [OpenAI Platform](https://platform.openai.com/api-keys) 获取）
3. 选择要使用的AI模型
4. 调整其他参数（可选）

### 3. 运行应用

#### Web版本
```bash
npm run dev
```
然后在浏览器中打开 http://localhost:1420

#### 桌面应用
```bash
npm run tauri dev
```

### 4. 构建应用

#### Web版本
```bash
npm run build
```

#### 桌面应用
```bash
npm run tauri build
```

## 使用指南

### 基本对话
1. 在输入框中输入您的问题
2. 按Enter键或点击发送按钮
3. AI将实时回复您的问题

### 文件上传
1. 点击输入框左侧的附件按钮，或直接拖拽文件到聊天区域
2. 支持的文件类型：
   - **图片**: JPG, PNG, GIF, WebP
   - **文档**: PDF, TXT, Markdown, CSV
3. 上传后可以询问AI关于文件内容的问题

### 会话管理
- **新建会话**: 点击侧边栏的"New Chat"按钮
- **重命名会话**: 悬停在会话上，点击编辑图标
- **删除会话**: 悬停在会话上，点击删除图标
- **切换会话**: 直接点击侧边栏中的会话

### 导出/导入数据
- **导出**: 点击侧边栏底部的"Export"按钮，下载JSON格式的会话数据
- **导入**: 点击"Import"按钮，选择之前导出的JSON文件

## 技术栈

- **前端框架**: SvelteKit 2.x
- **桌面应用**: Tauri 2.x
- **样式框架**: Tailwind CSS
- **图标库**: Lucide Svelte
- **AI服务**: OpenAI API
- **文件处理**: pdf-parse (PDF解析)

## 项目结构

```
src/
├── lib/
│   ├── components/          # Svelte组件
│   │   ├── ChatMessage.svelte      # 消息组件
│   │   ├── ChatInput.svelte        # 输入组件
│   │   ├── Sidebar.svelte          # 侧边栏组件
│   │   └── SettingsModal.svelte    # 设置模态框
│   ├── services/           # 服务层
│   │   ├── openai.js              # OpenAI API服务
│   │   └── fileProcessor.js       # 文件处理服务
│   └── stores/             # 状态管理
│       ├── messages.js            # 消息状态
│       └── settings.js            # 设置状态
├── routes/                 # 路由页面
│   ├── +layout.svelte             # 布局组件
│   └── +page.svelte               # 主页面
└── app.css                # 全局样式
```

## 开发说明

### 添加新功能
1. 在 `src/lib/components/` 中创建新组件
2. 在 `src/lib/services/` 中添加相关服务
3. 在 `src/lib/stores/` 中管理状态
4. 在主页面中集成新功能

### 样式定制
- 修改 `tailwind.config.js` 自定义主题
- 在 `src/app.css` 中添加全局样式
- 使用Tailwind CSS类进行组件样式设计

### API集成
- 在 `src/lib/services/openai.js` 中扩展OpenAI功能
- 支持其他AI服务提供商的API

## 注意事项

1. **API密钥安全**: 请妥善保管您的OpenAI API密钥，不要在公共场所或代码中暴露
2. **文件大小限制**: 大型PDF文件可能需要较长处理时间
3. **网络连接**: 需要稳定的网络连接来访问OpenAI API
4. **浏览器兼容性**: 建议使用现代浏览器（Chrome、Firefox、Safari、Edge）

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个项目！
