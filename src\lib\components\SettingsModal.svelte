<script>
  import { createEventDispatcher } from 'svelte';
  import { X, Eye, EyeOff, Save, RefreshCw, Info } from 'lucide-svelte';
  import { settings, saveSettings } from '../stores/settings.js';
  import { initializeOpenAI, getAvailableModels } from '../services/openai.js';
  import { getModelConfig, getModelDisplayName, getModelTypeLabel, isReasoningModel } from '../utils/modelUtils.js';
  
  const dispatch = createEventDispatcher();
  
  export let isOpen = false;
  
  let localSettings = { ...$settings };
  let showApiKey = false;
  let loading = false;
  let availableModels = [
    { id: 'gpt-4o', object: 'model' },
    { id: 'gpt-4o-mini', object: 'model' },
    { id: 'gpt-4-turbo', object: 'model' },
    { id: 'gpt-3.5-turbo', object: 'model' }
  ];
  
  // 当设置变化时更新本地设置
  $: if (isOpen) {
    localSettings = { ...$settings };
  }
  
  // 保存设置
  async function handleSave() {
    try {
      loading = true;
      
      // 如果API密钥改变了，重新初始化OpenAI客户端
      if (localSettings.apiKey && localSettings.apiKey !== $settings.apiKey) {
        await initializeOpenAI(localSettings.apiKey, localSettings.useOpenRouter);
      }
      
      saveSettings(localSettings);
      dispatch('close');
    } catch (error) {
      alert(`Failed to save settings: ${error.message}`);
    } finally {
      loading = false;
    }
  }
  
  // 测试API连接
  async function testConnection() {
    if (!localSettings.apiKey) {
      alert('Please enter an API key first');
      return;
    }

    try {
      loading = true;
      await initializeOpenAI(localSettings.apiKey, localSettings.useOpenRouter);
      const models = await getAvailableModels();
      availableModels = models;
      alert('Connection successful! Available models updated.');
    } catch (error) {
      alert(`Connection failed: ${error.message}`);
    } finally {
      loading = false;
    }
  }
  
  // 重置设置
  function resetSettings() {
    if (confirm('Are you sure you want to reset all settings to default?')) {
      localSettings = {
        apiKey: '',
        model: 'openai/gpt-4o',
        temperature: 0.7,
        maxTokens: 2000,
        systemPrompt: 'You are a helpful AI assistant.',
        darkMode: false,
        useOpenRouter: true
      };
    }
  }
  
  // 关闭模态框
  function close() {
    dispatch('close');
  }
  
  // 处理背景点击
  function handleBackdropClick(event) {
    if (event.target === event.currentTarget) {
      close();
    }
  }
</script>

{#if isOpen}
  <!-- 模态框背景 -->
  <div 
    class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
    on:click={handleBackdropClick}
  >
    <!-- 模态框内容 -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-hidden">
      <!-- 头部 -->
      <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
        <h2 class="text-xl font-semibold text-gray-900 dark:text-gray-100">
          Settings
        </h2>
        <button
          on:click={close}
          class="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
        >
          <X size={20} />
        </button>
      </div>
      
      <!-- 内容 -->
      <div class="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
        <div class="space-y-6">
          <!-- API 设置 -->
          <div>
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
              OpenAI API Configuration
            </h3>
            
            <!-- API Key -->
            <div class="mb-4">
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                API Key
              </label>
              <div class="relative">
                <input
                  type={showApiKey ? 'text' : 'password'}
                  bind:value={localSettings.apiKey}
                  placeholder="sk-..."
                  class="input-field pr-20"
                />
                <div class="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center gap-1">
                  <button
                    type="button"
                    on:click={() => showApiKey = !showApiKey}
                    class="p-1 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                  >
                    {#if showApiKey}
                      <EyeOff size={16} />
                    {:else}
                      <Eye size={16} />
                    {/if}
                  </button>
                  <button
                    type="button"
                    on:click={testConnection}
                    disabled={loading || !localSettings.apiKey}
                    class="p-1 text-blue-500 hover:text-blue-700 disabled:text-gray-400"
                    title="Test connection"
                  >
                    <RefreshCw size={16} class={loading ? 'animate-spin' : ''} />
                  </button>
                </div>
              </div>
              <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                Get your API key from <a href="https://openrouter.ai/keys" target="_blank" class="text-blue-500 hover:underline">OpenRouter</a>
                (supports multiple AI models with competitive pricing)
              </p>
              <p class="text-xs text-gray-400 dark:text-gray-500 mt-1">
                Alternative: <a href="https://platform.openai.com/api-keys" target="_blank" class="text-blue-500 hover:underline">OpenAI Platform</a>
                (requires changing baseURL in code)
              </p>
            </div>
            
            <!-- Model Selection -->
            <div class="mb-4">
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Model Selection
              </label>

              <!-- 预设模型选择 -->
              <div class="mb-3">
                <label class="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                  Quick Select (Popular Models)
                </label>
                <select
                  on:change={(e) => localSettings.model = e.target.value}
                  class="input-field text-sm"
                >
                  <option value="">-- Select a popular model --</option>
                  <optgroup label="🧠 Reasoning Models (Advanced)">
                    <option value="openai/o3-mini">OpenAI o3-mini (Reasoning)</option>
                    <option value="openai/o1">OpenAI o1 (Reasoning)</option>
                    <option value="openai/o1-mini">OpenAI o1-mini (Reasoning)</option>
                    <option value="google/gemini-2.0-flash-thinking-exp">Gemini 2.0 Flash Thinking</option>
                    <option value="deepseek/deepseek-r1">DeepSeek R1 (Reasoning)</option>
                  </optgroup>
                  <optgroup label="⚡ Fast Models">
                    <option value="openai/gpt-4o">OpenAI GPT-4o</option>
                    <option value="openai/gpt-4o-mini">OpenAI GPT-4o-mini</option>
                    <option value="anthropic/claude-3.5-sonnet">Claude 3.5 Sonnet</option>
                    <option value="anthropic/claude-3.5-haiku">Claude 3.5 Haiku</option>
                    <option value="google/gemini-pro-1.5">Gemini Pro 1.5</option>
                  </optgroup>
                  <optgroup label="🔬 Experimental">
                    <option value="google/gemini-2.5-pro-exp">Gemini 2.5 Pro (Experimental)</option>
                    <option value="anthropic/claude-3-opus">Claude 3 Opus</option>
                    <option value="meta-llama/llama-3.3-70b-instruct">Llama 3.3 70B</option>
                    <option value="qwen/qwen-2.5-72b-instruct">Qwen 2.5 72B</option>
                  </optgroup>
                </select>
              </div>

              <!-- 自定义模型输入 -->
              <div>
                <label class="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                  Custom Model Name
                </label>
                <input
                  type="text"
                  bind:value={localSettings.model}
                  placeholder="e.g., openai/gpt-4o, anthropic/claude-3.5-sonnet"
                  class="input-field text-sm"
                />
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Enter any model name supported by OpenRouter. Format: provider/model-name
                </p>
              </div>

              <!-- 模型信息显示 -->
              {#if localSettings.model}
                {@const modelConfig = getModelConfig(localSettings.model)}
                {@const modelLabel = getModelTypeLabel(localSettings.model)}
                <div class="mt-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg border">
                  <div class="flex items-center gap-2 mb-2">
                    <Info size={16} class="text-blue-500" />
                    <span class="font-medium text-sm text-gray-900 dark:text-gray-100">
                      {getModelDisplayName(localSettings.model)}
                    </span>
                    <span class="px-2 py-1 text-xs rounded-full {modelLabel.color}">
                      {modelLabel.text}
                    </span>
                    {#if modelConfig.isReasoning}
                      <span class="px-2 py-1 text-xs rounded-full bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
                        🧠 Reasoning
                      </span>
                    {/if}
                  </div>

                  <div class="text-xs text-gray-600 dark:text-gray-400 space-y-1">
                    <div>Recommended Temperature: {modelConfig.recommendedTemperature}</div>
                    <div>Recommended Max Tokens: {modelConfig.recommendedMaxTokens}</div>
                    {#if modelConfig.specialInstructions.length > 0}
                      <div class="mt-2">
                        <div class="font-medium mb-1">特性:</div>
                        <ul class="list-disc list-inside space-y-0.5">
                          {#each modelConfig.specialInstructions as instruction}
                            <li>{instruction}</li>
                          {/each}
                        </ul>
                      </div>
                    {/if}
                  </div>

                  {#if modelConfig.isReasoning}
                    <div class="mt-2 p-2 bg-purple-50 dark:bg-purple-900/20 rounded border border-purple-200 dark:border-purple-700">
                      <div class="text-xs text-purple-800 dark:text-purple-200">
                        <div class="font-medium mb-1">🧠 思考模型特性:</div>
                        <ul class="list-disc list-inside space-y-0.5">
                          <li>会显示详细的推理过程</li>
                          <li>响应时间较长，请耐心等待</li>
                          <li>适合复杂的逻辑推理任务</li>
                          <li>建议使用较高的温度值 (1.0)</li>
                        </ul>
                      </div>
                    </div>
                  {/if}

                  <!-- 快速应用推荐设置按钮 -->
                  <button
                    type="button"
                    on:click={() => {
                      localSettings.temperature = modelConfig.recommendedTemperature;
                      localSettings.maxTokens = modelConfig.recommendedMaxTokens;
                    }}
                    class="mt-2 px-3 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
                  >
                    应用推荐设置
                  </button>
                </div>
              {/if}
            </div>
          </div>
          
          <!-- 生成参数 -->
          <div>
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
              Generation Parameters
            </h3>
            
            <!-- Temperature -->
            <div class="mb-4">
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Temperature: {localSettings.temperature}
              </label>
              <input
                type="range"
                min="0"
                max="2"
                step="0.1"
                bind:value={localSettings.temperature}
                class="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer slider"
              />
              <div class="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1">
                <span>Focused (0)</span>
                <span>Balanced (1)</span>
                <span>Creative (2)</span>
              </div>
            </div>
            
            <!-- Max Tokens -->
            <div class="mb-4">
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Max Tokens
              </label>
              <input
                type="number"
                min="1"
                max="4000"
                bind:value={localSettings.maxTokens}
                class="input-field"
              />
              <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                Maximum number of tokens to generate (1-4000)
              </p>
            </div>
          </div>
          
          <!-- System Prompt -->
          <div>
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
              System Prompt
            </h3>
            <textarea
              bind:value={localSettings.systemPrompt}
              rows="4"
              class="input-field resize-none"
              placeholder="You are a helpful AI assistant..."
            ></textarea>
            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
              This message will be sent at the beginning of every conversation to set the AI's behavior
            </p>
          </div>
        </div>
      </div>
      
      <!-- 底部按钮 -->
      <div class="flex items-center justify-between p-6 border-t border-gray-200 dark:border-gray-700">
        <button
          on:click={resetSettings}
          class="btn-secondary text-red-600 dark:text-red-400"
        >
          Reset to Default
        </button>
        
        <div class="flex items-center gap-3">
          <button
            on:click={close}
            class="btn-secondary"
          >
            Cancel
          </button>
          <button
            on:click={handleSave}
            disabled={loading}
            class="btn-primary flex items-center gap-2"
          >
            <Save size={16} />
            Save Settings
          </button>
        </div>
      </div>
    </div>
  </div>
{/if}

<style>
  .slider::-webkit-slider-thumb {
    appearance: none;
    height: 20px;
    width: 20px;
    border-radius: 50%;
    background: #3b82f6;
    cursor: pointer;
  }
  
  .slider::-moz-range-thumb {
    height: 20px;
    width: 20px;
    border-radius: 50%;
    background: #3b82f6;
    cursor: pointer;
    border: none;
  }
</style>
