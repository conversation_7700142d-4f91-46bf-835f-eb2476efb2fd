import { writable } from 'svelte/store';
import { browser } from '$app/environment';

// 默认设置
const defaultSettings = {
  apiKey: '',
  model: 'gpt-4o',
  temperature: 0.7,
  maxTokens: 2000,
  systemPrompt: 'You are a helpful AI assistant.',
  darkMode: false
};

// 从localStorage加载设置
function loadSettings() {
  if (browser) {
    try {
      const saved = localStorage.getItem('ai-chat-settings');
      if (saved) {
        return { ...defaultSettings, ...JSON.parse(saved) };
      }
    } catch (error) {
      console.error('Failed to load settings:', error);
    }
  }
  return defaultSettings;
}

// 创建设置store
export const settings = writable(loadSettings());

// 保存设置到localStorage
export function saveSettings(newSettings) {
  if (browser) {
    try {
      localStorage.setItem('ai-chat-settings', JSON.stringify(newSettings));
      settings.set(newSettings);
    } catch (error) {
      console.error('Failed to save settings:', error);
    }
  }
}

// 更新单个设置
export function updateSetting(key, value) {
  settings.update(current => {
    const updated = { ...current, [key]: value };
    if (browser) {
      try {
        localStorage.setItem('ai-chat-settings', JSON.stringify(updated));
      } catch (error) {
        console.error('Failed to save setting:', error);
      }
    }
    return updated;
  });
}
