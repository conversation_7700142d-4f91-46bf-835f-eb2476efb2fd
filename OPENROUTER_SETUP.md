# OpenRouter 配置指南

本应用已配置为使用 OpenRouter API，这样您可以访问多种AI模型，包括OpenAI、Anthropic、Google、Meta等公司的模型。

## 🚀 快速开始

### 1. 获取 OpenRouter API 密钥
1. 访问 [OpenRouter](https://openrouter.ai/)
2. 注册账户或登录
3. 前往 [API Keys 页面](https://openrouter.ai/keys)
4. 创建新的API密钥
5. 复制密钥（格式类似：`sk-or-v1-...`）

### 2. 配置应用
1. 启动应用
2. 点击"Configure API Key"按钮
3. 粘贴您的OpenRouter API密钥
4. 选择要使用的AI模型
5. 点击"Save Settings"

## 🤖 支持的模型

OpenRouter支持众多AI模型，包括：

### OpenAI 模型
- `openai/gpt-4o` - 最新的GPT-4o模型
- `openai/gpt-4o-mini` - 更快更便宜的GPT-4o版本
- `openai/gpt-4-turbo` - GPT-4 Turbo
- `openai/gpt-3.5-turbo` - GPT-3.5 Turbo

### Anthropic 模型
- `anthropic/claude-3.5-sonnet` - Claude 3.5 Sonnet
- `anthropic/claude-3-haiku` - Claude 3 Haiku

### Google 模型
- `google/gemini-pro-1.5` - Gemini Pro 1.5

### Meta 模型
- `meta-llama/llama-3.1-70b-instruct` - Llama 3.1 70B

### 其他模型
- `mistralai/mistral-7b-instruct` - Mistral 7B
- `qwen/qwen-2-72b-instruct` - Qwen 2 72B

## 💰 定价优势

OpenRouter的主要优势：
- **统一API**: 一个API密钥访问多种模型
- **竞争性定价**: 通常比直接使用各厂商API更便宜
- **无需多个账户**: 不需要分别注册OpenAI、Anthropic等账户
- **灵活切换**: 可以轻松在不同模型间切换

## 🔧 技术细节

### API配置
应用已配置为使用OpenRouter的API端点：
- **Base URL**: `https://openrouter.ai/api/v1`
- **兼容性**: 完全兼容OpenAI API格式
- **Headers**: 包含应用标识信息

### 模型格式
OpenRouter使用特定的模型命名格式：
```
提供商/模型名称
例如：openai/gpt-4o, anthropic/claude-3.5-sonnet
```

## 🛠️ 故障排除

### 常见问题

1. **API密钥无效**
   - 确保密钥格式正确（以`sk-or-v1-`开头）
   - 检查密钥是否已激活
   - 确认账户有足够余额

2. **模型不可用**
   - 某些模型可能暂时不可用
   - 尝试切换到其他模型
   - 检查OpenRouter状态页面

3. **请求失败**
   - 检查网络连接
   - 确认API密钥权限
   - 查看浏览器控制台错误信息

### 调试步骤
1. 打开浏览器开发者工具
2. 查看Console标签页的错误信息
3. 检查Network标签页的API请求
4. 确认请求头包含正确的API密钥

## 🔄 切换回OpenAI

如果您想直接使用OpenAI API而不是OpenRouter：

1. 修改 `src/lib/services/openai.js` 文件
2. 在 `initializeOpenAI` 函数中设置 `useOpenRouter = false`
3. 使用OpenAI的API密钥（格式：`sk-...`）

## 📚 更多资源

- [OpenRouter 官方文档](https://openrouter.ai/docs)
- [支持的模型列表](https://openrouter.ai/models)
- [定价信息](https://openrouter.ai/models)
- [API参考](https://openrouter.ai/docs/api-reference)

## 💡 提示

- 建议从便宜的模型开始测试（如`openai/gpt-3.5-turbo`）
- 不同模型有不同的特点和定价，选择适合您需求的模型
- 可以在对话中随时切换模型
- 定期检查账户余额和使用情况
