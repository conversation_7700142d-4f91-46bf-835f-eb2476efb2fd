import pdfParse from 'pdf-parse';

// 支持的文件类型
export const SUPPORTED_FILE_TYPES = {
  PDF: ['application/pdf'],
  IMAGE: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'],
  TEXT: ['text/plain', 'text/markdown', 'text/csv']
};

// 检查文件类型
export function getFileType(file) {
  const mimeType = file.type;
  
  if (SUPPORTED_FILE_TYPES.PDF.includes(mimeType)) {
    return 'PDF';
  } else if (SUPPORTED_FILE_TYPES.IMAGE.includes(mimeType)) {
    return 'IMAGE';
  } else if (SUPPORTED_FILE_TYPES.TEXT.includes(mimeType)) {
    return 'TEXT';
  }
  
  return 'UNSUPPORTED';
}

// 检查文件是否支持
export function isFileSupported(file) {
  return getFileType(file) !== 'UNSUPPORTED';
}

// 读取PDF文件内容
export async function readPDFContent(file) {
  try {
    const arrayBuffer = await file.arrayBuffer();
    const data = await pdfParse(arrayBuffer);
    
    return {
      text: data.text,
      pages: data.numpages,
      info: data.info,
      metadata: data.metadata
    };
  } catch (error) {
    console.error('PDF parsing error:', error);
    throw new Error(`Failed to read PDF: ${error.message}`);
  }
}

// 读取文本文件内容
export async function readTextContent(file) {
  try {
    const text = await file.text();
    return {
      text,
      size: file.size,
      type: file.type
    };
  } catch (error) {
    console.error('Text reading error:', error);
    throw new Error(`Failed to read text file: ${error.message}`);
  }
}

// 处理图片文件
export async function processImage(file) {
  try {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      
      reader.onload = (event) => {
        const img = new Image();
        
        img.onload = () => {
          resolve({
            dataUrl: event.target.result,
            width: img.width,
            height: img.height,
            size: file.size,
            type: file.type,
            name: file.name
          });
        };
        
        img.onerror = () => {
          reject(new Error('Failed to load image'));
        };
        
        img.src = event.target.result;
      };
      
      reader.onerror = () => {
        reject(new Error('Failed to read image file'));
      };
      
      reader.readAsDataURL(file);
    });
  } catch (error) {
    console.error('Image processing error:', error);
    throw new Error(`Failed to process image: ${error.message}`);
  }
}

// 压缩图片（如果需要）
export async function compressImage(file, maxWidth = 1024, quality = 0.8) {
  try {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      
      reader.onload = (event) => {
        const img = new Image();
        
        img.onload = () => {
          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d');
          
          // 计算新尺寸
          let { width, height } = img;
          if (width > maxWidth) {
            height = (height * maxWidth) / width;
            width = maxWidth;
          }
          
          canvas.width = width;
          canvas.height = height;
          
          // 绘制压缩后的图片
          ctx.drawImage(img, 0, 0, width, height);
          
          canvas.toBlob(
            (blob) => {
              if (blob) {
                resolve(new File([blob], file.name, {
                  type: file.type,
                  lastModified: Date.now()
                }));
              } else {
                reject(new Error('Failed to compress image'));
              }
            },
            file.type,
            quality
          );
        };
        
        img.onerror = () => {
          reject(new Error('Failed to load image for compression'));
        };
        
        img.src = event.target.result;
      };
      
      reader.onerror = () => {
        reject(new Error('Failed to read image file for compression'));
      };
      
      reader.readAsDataURL(file);
    });
  } catch (error) {
    console.error('Image compression error:', error);
    throw new Error(`Failed to compress image: ${error.message}`);
  }
}

// 统一文件处理函数
export async function processFile(file) {
  const fileType = getFileType(file);
  
  if (fileType === 'UNSUPPORTED') {
    throw new Error(`Unsupported file type: ${file.type}`);
  }
  
  const baseInfo = {
    name: file.name,
    size: file.size,
    type: file.type,
    lastModified: file.lastModified,
    fileType
  };
  
  try {
    switch (fileType) {
      case 'PDF':
        const pdfContent = await readPDFContent(file);
        return { ...baseInfo, content: pdfContent };
        
      case 'IMAGE':
        const imageContent = await processImage(file);
        return { ...baseInfo, content: imageContent };
        
      case 'TEXT':
        const textContent = await readTextContent(file);
        return { ...baseInfo, content: textContent };
        
      default:
        throw new Error(`Unsupported file type: ${fileType}`);
    }
  } catch (error) {
    console.error('File processing error:', error);
    throw error;
  }
}

// 格式化文件大小
export function formatFileSize(bytes) {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
