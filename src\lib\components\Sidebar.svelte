<script>
  import { createEventDispatcher } from 'svelte';
  import { 
    Plus, 
    MessageSquare, 
    Settings, 
    Trash2, 
    Edit3, 
    Check, 
    X,
    Download,
    Upload,
    Moon,
    Sun
  } from 'lucide-svelte';
  import { sessions, currentSessionId, createNewSession, switchSession, deleteSession, renameSession } from '../stores/messages.js';
  import { settings, updateSetting } from '../stores/settings.js';
  
  const dispatch = createEventDispatcher();
  
  export let isOpen = true;
  
  let editingSessionId = null;
  let editingTitle = '';
  
  // 开始编辑会话标题
  function startEditing(sessionId, currentTitle) {
    editingSessionId = sessionId;
    editingTitle = currentTitle;
  }
  
  // 保存编辑
  function saveEdit() {
    if (editingTitle.trim()) {
      renameSession(editingSessionId, editingTitle.trim());
    }
    editingSessionId = null;
    editingTitle = '';
  }
  
  // 取消编辑
  function cancelEdit() {
    editingSessionId = null;
    editingTitle = '';
  }
  
  // 处理键盘事件
  function handleKeydown(event) {
    if (event.key === 'Enter') {
      saveEdit();
    } else if (event.key === 'Escape') {
      cancelEdit();
    }
  }
  
  // 格式化日期
  function formatDate(timestamp) {
    const date = new Date(timestamp);
    const now = new Date();
    const diffTime = now - date;
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) {
      return 'Today';
    } else if (diffDays === 1) {
      return 'Yesterday';
    } else if (diffDays < 7) {
      return `${diffDays} days ago`;
    } else {
      return date.toLocaleDateString();
    }
  }
  
  // 切换暗色模式
  function toggleDarkMode() {
    updateSetting('darkMode', !$settings.darkMode);
    
    if ($settings.darkMode) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  }
  
  // 获取排序后的会话列表
  $: sortedSessions = Object.values($sessions).sort((a, b) => 
    new Date(b.updatedAt) - new Date(a.updatedAt)
  );
</script>

<div class="h-full bg-gray-50 dark:bg-gray-900 border-r border-gray-200 dark:border-gray-700 flex flex-col {isOpen ? 'w-80' : 'w-0 overflow-hidden'}">
  <!-- Header -->
  <div class="p-4 border-b border-gray-200 dark:border-gray-700">
    <div class="flex items-center justify-between mb-4">
      <h1 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
        AI Chat
      </h1>
      
      <div class="flex items-center gap-2">
        <!-- 暗色模式切换 -->
        <button
          on:click={toggleDarkMode}
          class="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-700 rounded-lg transition-colors"
          title="Toggle dark mode"
        >
          {#if $settings.darkMode}
            <Sun size={18} />
          {:else}
            <Moon size={18} />
          {/if}
        </button>
        
        <!-- 设置按钮 -->
        <button
          on:click={() => dispatch('openSettings')}
          class="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-700 rounded-lg transition-colors"
          title="Settings"
        >
          <Settings size={18} />
        </button>
      </div>
    </div>
    
    <!-- 新建聊天按钮 -->
    <button
      on:click={createNewSession}
      class="w-full flex items-center gap-3 p-3 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors"
    >
      <Plus size={18} />
      <span>New Chat</span>
    </button>
  </div>
  
  <!-- 会话列表 -->
  <div class="flex-1 overflow-y-auto scrollbar-thin">
    <div class="p-2">
      {#each sortedSessions as session (session.id)}
        <div class="mb-1">
          <div class="group flex items-center gap-2 p-3 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors {
            $currentSessionId === session.id ? 'bg-gray-100 dark:bg-gray-800' : ''
          }">
            <!-- 会话图标 -->
            <MessageSquare size={16} class="text-gray-500 dark:text-gray-400 flex-shrink-0" />
            
            <!-- 会话标题 -->
            <div class="flex-1 min-w-0">
              {#if editingSessionId === session.id}
                <input
                  bind:value={editingTitle}
                  on:keydown={handleKeydown}
                  on:blur={saveEdit}
                  class="w-full bg-transparent border-0 outline-none text-sm font-medium text-gray-900 dark:text-gray-100"
                  autofocus
                />
              {:else}
                <button
                  on:click={() => switchSession(session.id)}
                  class="w-full text-left"
                >
                  <div class="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                    {session.title}
                  </div>
                  <div class="text-xs text-gray-500 dark:text-gray-400">
                    {formatDate(session.updatedAt)}
                  </div>
                </button>
              {/if}
            </div>
            
            <!-- 操作按钮 -->
            <div class="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
              {#if editingSessionId === session.id}
                <button
                  on:click={saveEdit}
                  class="p-1 text-green-600 hover:bg-green-100 dark:hover:bg-green-900/20 rounded"
                  title="Save"
                >
                  <Check size={14} />
                </button>
                <button
                  on:click={cancelEdit}
                  class="p-1 text-red-600 hover:bg-red-100 dark:hover:bg-red-900/20 rounded"
                  title="Cancel"
                >
                  <X size={14} />
                </button>
              {:else}
                <button
                  on:click={() => startEditing(session.id, session.title)}
                  class="p-1 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-700 rounded"
                  title="Rename"
                >
                  <Edit3 size={14} />
                </button>
                <button
                  on:click={() => deleteSession(session.id)}
                  class="p-1 text-red-500 hover:text-red-700 hover:bg-red-100 dark:hover:bg-red-900/20 rounded"
                  title="Delete"
                >
                  <Trash2 size={14} />
                </button>
              {/if}
            </div>
          </div>
        </div>
      {/each}
      
      {#if sortedSessions.length === 0}
        <div class="text-center py-8 text-gray-500 dark:text-gray-400">
          <MessageSquare size={48} class="mx-auto mb-3 opacity-50" />
          <p class="text-sm">No conversations yet</p>
          <p class="text-xs">Start a new chat to begin</p>
        </div>
      {/if}
    </div>
  </div>
  
  <!-- Footer -->
  <div class="p-4 border-t border-gray-200 dark:border-gray-700">
    <div class="flex items-center gap-2">
      <button
        on:click={() => dispatch('exportSessions')}
        class="flex-1 flex items-center justify-center gap-2 p-2 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors"
        title="Export sessions"
      >
        <Download size={16} />
        <span>Export</span>
      </button>
      
      <button
        on:click={() => dispatch('importSessions')}
        class="flex-1 flex items-center justify-center gap-2 p-2 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors"
        title="Import sessions"
      >
        <Upload size={16} />
        <span>Import</span>
      </button>
    </div>
  </div>
</div>
