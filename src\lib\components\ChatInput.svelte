<script>
  import { createEventDispatcher } from 'svelte';
  import { Send, Paperclip, X, FileText, Image } from 'lucide-svelte';
  import { processFile, isFileSupported, formatFileSize } from '../services/fileProcessor.js';
  
  const dispatch = createEventDispatcher();
  
  export let disabled = false;
  export let placeholder = "Type your message...";
  
  let message = '';
  let fileInput;
  let attachedFiles = [];
  let dragOver = false;
  let processing = false;
  
  // 发送消息
  async function sendMessage() {
    if (!message.trim() && attachedFiles.length === 0) return;
    if (disabled || processing) return;
    
    const messageData = {
      text: message.trim(),
      files: attachedFiles
    };
    
    dispatch('send', messageData);
    
    // 清空输入
    message = '';
    attachedFiles = [];
  }
  
  // 处理键盘事件
  function handleKeydown(event) {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      sendMessage();
    }
  }
  
  // 处理文件选择
  async function handleFileSelect(event) {
    const files = Array.from(event.target.files || []);
    await processFiles(files);
    // 清空input以允许重复选择同一文件
    if (fileInput) fileInput.value = '';
  }
  
  // 处理拖拽
  function handleDragOver(event) {
    event.preventDefault();
    dragOver = true;
  }
  
  function handleDragLeave(event) {
    event.preventDefault();
    dragOver = false;
  }
  
  async function handleDrop(event) {
    event.preventDefault();
    dragOver = false;
    
    const files = Array.from(event.dataTransfer.files);
    await processFiles(files);
  }
  
  // 处理文件
  async function processFiles(files) {
    processing = true;
    
    for (const file of files) {
      if (!isFileSupported(file)) {
        alert(`Unsupported file type: ${file.type}`);
        continue;
      }
      
      try {
        const processedFile = await processFile(file);
        attachedFiles = [...attachedFiles, processedFile];
      } catch (error) {
        console.error('File processing error:', error);
        alert(`Failed to process file ${file.name}: ${error.message}`);
      }
    }
    
    processing = false;
  }
  
  // 移除附件
  function removeFile(index) {
    attachedFiles = attachedFiles.filter((_, i) => i !== index);
  }
  
  // 打开文件选择器
  function openFileSelector() {
    if (fileInput) {
      fileInput.click();
    }
  }
</script>

<div class="border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
  <!-- 附件预览 -->
  {#if attachedFiles.length > 0}
    <div class="p-4 border-b border-gray-200 dark:border-gray-700">
      <div class="flex flex-wrap gap-2">
        {#each attachedFiles as file, index}
          <div class="flex items-center gap-2 bg-gray-100 dark:bg-gray-700 rounded-lg p-2 text-sm">
            {#if file.fileType === 'IMAGE'}
              <Image size={16} class="text-blue-500" />
            {:else}
              <FileText size={16} class="text-green-500" />
            {/if}
            
            <span class="font-medium">{file.name}</span>
            <span class="text-xs text-gray-500">({formatFileSize(file.size)})</span>
            
            <button
              on:click={() => removeFile(index)}
              class="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded"
              title="Remove file"
            >
              <X size={14} />
            </button>
          </div>
        {/each}
      </div>
    </div>
  {/if}
  
  <!-- 输入区域 -->
  <div 
    class="p-4 {dragOver ? 'bg-blue-50 dark:bg-blue-900/20' : ''}"
    on:dragover={handleDragOver}
    on:dragleave={handleDragLeave}
    on:drop={handleDrop}
  >
    {#if dragOver}
      <div class="text-center py-8 text-blue-600 dark:text-blue-400">
        <div class="text-lg font-medium">Drop files here</div>
        <div class="text-sm">Supports PDF, images, and text files</div>
      </div>
    {:else}
      <div class="flex items-end gap-3">
        <!-- 文件附件按钮 -->
        <button
          on:click={openFileSelector}
          class="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
          title="Attach file"
          {disabled}
        >
          <Paperclip size={20} />
        </button>
        
        <!-- 消息输入框 -->
        <div class="flex-1">
          <textarea
            bind:value={message}
            on:keydown={handleKeydown}
            {placeholder}
            {disabled}
            class="w-full resize-none border-0 bg-gray-100 dark:bg-gray-700 rounded-lg px-4 py-3 focus:ring-2 focus:ring-primary-500 focus:bg-white dark:focus:bg-gray-600 transition-all duration-200 max-h-32"
            rows="1"
            style="field-sizing: content;"
          ></textarea>
        </div>
        
        <!-- 发送按钮 -->
        <button
          on:click={sendMessage}
          disabled={disabled || processing || (!message.trim() && attachedFiles.length === 0)}
          class="p-3 bg-primary-600 hover:bg-primary-700 disabled:bg-gray-300 dark:disabled:bg-gray-600 text-white rounded-lg transition-colors duration-200 disabled:cursor-not-allowed"
          title="Send message"
        >
          <Send size={20} />
        </button>
      </div>
    {/if}
  </div>
  
  <!-- 处理状态 -->
  {#if processing}
    <div class="px-4 pb-2">
      <div class="text-sm text-gray-500 dark:text-gray-400">
        Processing files<span class="loading-dots"></span>
      </div>
    </div>
  {/if}
</div>

<!-- 隐藏的文件输入 -->
<input
  bind:this={fileInput}
  type="file"
  multiple
  accept=".pdf,.txt,.md,.csv,.jpg,.jpeg,.png,.gif,.webp"
  on:change={handleFileSelect}
  class="hidden"
/>

<style>
  textarea {
    field-sizing: content;
    min-height: 44px;
  }
</style>
