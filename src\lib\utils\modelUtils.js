// 思考模型检测和处理工具

// 思考模型列表
export const REASONING_MODELS = [
  'openai/o3-mini',
  'openai/o3',
  'openai/o1',
  'openai/o1-mini',
  'openai/o1-preview',
  'google/gemini-2.0-flash-thinking-exp',
  'google/gemini-2.5-pro-exp',
  'deepseek/deepseek-r1',
  'deepseek/deepseek-r1-distill-llama-70b',
  'qwen/qwen-2.5-72b-instruct:thinking'
];

// 检测是否为思考模型
export function isReasoningModel(modelName) {
  if (!modelName) return false;
  
  const normalizedModel = modelName.toLowerCase();
  
  // 直接匹配
  if (REASONING_MODELS.some(model => normalizedModel === model.toLowerCase())) {
    return true;
  }
  
  // 模糊匹配关键词
  const reasoningKeywords = [
    'o1', 'o3', 'thinking', 'reasoning', 'r1', 'chain-of-thought', 'cot'
  ];
  
  return reasoningKeywords.some(keyword => normalizedModel.includes(keyword));
}

// 获取模型的推荐配置
export function getModelConfig(modelName) {
  const isReasoning = isReasoningModel(modelName);
  
  if (isReasoning) {
    return {
      isReasoning: true,
      recommendedTemperature: 1.0, // 思考模型通常使用较高温度
      recommendedMaxTokens: 8000, // 思考模型需要更多token
      supportsStreaming: true,
      supportsSystemPrompt: true,
      specialInstructions: [
        '思考模型会显示推理过程',
        '响应时间可能较长',
        '建议使用较高的温度值',
        '适合复杂推理任务'
      ]
    };
  }
  
  // 根据模型名称返回不同配置
  const modelLower = modelName.toLowerCase();
  
  if (modelLower.includes('claude')) {
    return {
      isReasoning: false,
      recommendedTemperature: 0.7,
      recommendedMaxTokens: 4000,
      supportsStreaming: true,
      supportsSystemPrompt: true,
      specialInstructions: [
        'Claude模型擅长分析和创作',
        '支持长文本处理',
        '适合复杂对话'
      ]
    };
  }
  
  if (modelLower.includes('gemini')) {
    return {
      isReasoning: false,
      recommendedTemperature: 0.8,
      recommendedMaxTokens: 3000,
      supportsStreaming: true,
      supportsSystemPrompt: true,
      specialInstructions: [
        'Gemini模型支持多模态',
        '擅长代码和数学',
        '响应速度较快'
      ]
    };
  }
  
  if (modelLower.includes('llama')) {
    return {
      isReasoning: false,
      recommendedTemperature: 0.7,
      recommendedMaxTokens: 2000,
      supportsStreaming: true,
      supportsSystemPrompt: true,
      specialInstructions: [
        'Llama模型开源且强大',
        '适合通用对话',
        '成本效益高'
      ]
    };
  }
  
  // 默认配置（GPT等）
  return {
    isReasoning: false,
    recommendedTemperature: 0.7,
    recommendedMaxTokens: 2000,
    supportsStreaming: true,
    supportsSystemPrompt: true,
    specialInstructions: [
      '通用AI模型',
      '适合各种任务',
      '平衡性能和成本'
    ]
  };
}

// 格式化思考过程显示
export function formatThinkingProcess(content) {
  if (!content) return '';
  
  // 检测思考标记
  const thinkingPatterns = [
    /\<thinking\>(.*?)\<\/thinking\>/gs,
    /\*\*思考过程\*\*(.*?)\*\*\/思考过程\*\*/gs,
    /\[思考\](.*?)\[\/思考\]/gs,
    /\<think\>(.*?)\<\/think\>/gs
  ];
  
  let thinking = '';
  let response = content;
  
  for (const pattern of thinkingPatterns) {
    const matches = content.match(pattern);
    if (matches) {
      thinking = matches.map(match => 
        match.replace(pattern, '$1').trim()
      ).join('\n\n');
      response = content.replace(pattern, '').trim();
      break;
    }
  }
  
  return {
    thinking: thinking || '',
    response: response || content,
    hasThinking: !!thinking
  };
}

// 获取模型显示名称
export function getModelDisplayName(modelName) {
  if (!modelName) return 'Unknown Model';
  
  const displayNames = {
    'openai/gpt-4o': 'GPT-4o',
    'openai/gpt-4o-mini': 'GPT-4o Mini',
    'openai/o1': 'GPT-o1 (Reasoning)',
    'openai/o1-mini': 'GPT-o1 Mini (Reasoning)',
    'openai/o3-mini': 'GPT-o3 Mini (Reasoning)',
    'anthropic/claude-3.5-sonnet': 'Claude 3.5 Sonnet',
    'anthropic/claude-3.5-haiku': 'Claude 3.5 Haiku',
    'google/gemini-pro-1.5': 'Gemini Pro 1.5',
    'google/gemini-2.0-flash-thinking-exp': 'Gemini 2.0 Flash Thinking',
    'google/gemini-2.5-pro-exp': 'Gemini 2.5 Pro (Experimental)',
    'deepseek/deepseek-r1': 'DeepSeek R1 (Reasoning)',
    'meta-llama/llama-3.3-70b-instruct': 'Llama 3.3 70B'
  };
  
  return displayNames[modelName] || modelName;
}

// 获取模型类型标签
export function getModelTypeLabel(modelName) {
  if (isReasoningModel(modelName)) {
    return { text: 'Reasoning', color: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200' };
  }
  
  const modelLower = modelName.toLowerCase();
  
  if (modelLower.includes('claude')) {
    return { text: 'Anthropic', color: 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200' };
  }
  
  if (modelLower.includes('gemini')) {
    return { text: 'Google', color: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' };
  }
  
  if (modelLower.includes('llama')) {
    return { text: 'Meta', color: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' };
  }
  
  if (modelLower.includes('openai') || modelLower.includes('gpt')) {
    return { text: 'OpenAI', color: 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200' };
  }
  
  return { text: 'Other', color: 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200' };
}
