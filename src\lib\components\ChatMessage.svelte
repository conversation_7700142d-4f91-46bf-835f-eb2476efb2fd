<script>
  import { MESSAGE_ROLES, MESSAGE_TYPES } from '../stores/messages.js';
  import { User, Bot, FileText, Image, Copy, Check } from 'lucide-svelte';
  
  export let message;
  
  let copied = false;
  
  // 复制消息内容
  async function copyMessage() {
    try {
      await navigator.clipboard.writeText(message.content);
      copied = true;
      setTimeout(() => copied = false, 2000);
    } catch (error) {
      console.error('Failed to copy message:', error);
    }
  }
  
  // 格式化文件大小
  function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  // 格式化时间
  function formatTime(timestamp) {
    return new Date(timestamp).toLocaleTimeString([], {
      hour: '2-digit',
      minute: '2-digit'
    });
  }
  
  // 渲染Markdown（简单实现）
  function renderMarkdown(text) {
    return text
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      .replace(/`(.*?)`/g, '<code class="bg-gray-100 dark:bg-gray-700 px-1 rounded">$1</code>')
      .replace(/\n/g, '<br>');
  }
</script>

<div class="flex gap-3 p-4 {message.role === MESSAGE_ROLES.USER ? 'flex-row-reverse' : 'flex-row'}">
  <!-- Avatar -->
  <div class="flex-shrink-0">
    <div class="w-8 h-8 rounded-full flex items-center justify-center {
      message.role === MESSAGE_ROLES.USER 
        ? 'bg-primary-600 text-white' 
        : 'bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-300'
    }">
      {#if message.role === MESSAGE_ROLES.USER}
        <User size={16} />
      {:else}
        <Bot size={16} />
      {/if}
    </div>
  </div>
  
  <!-- Message Content -->
  <div class="flex-1 max-w-3xl">
    <div class="message-bubble {
      message.role === MESSAGE_ROLES.USER 
        ? 'message-user' 
        : 'message-assistant'
    }">
      <!-- File attachments -->
      {#if message.metadata?.file}
        <div class="mb-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg border">
          <div class="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-300">
            {#if message.metadata.file.fileType === 'IMAGE'}
              <Image size={16} />
            {:else}
              <FileText size={16} />
            {/if}
            <span class="font-medium">{message.metadata.file.name}</span>
            <span class="text-xs">({formatFileSize(message.metadata.file.size)})</span>
          </div>
          
          {#if message.metadata.file.fileType === 'IMAGE' && message.metadata.file.content?.dataUrl}
            <img 
              src={message.metadata.file.content.dataUrl} 
              alt={message.metadata.file.name}
              class="mt-2 max-w-full h-auto rounded-lg shadow-sm"
              style="max-height: 300px;"
            />
          {/if}
        </div>
      {/if}
      
      <!-- Message text -->
      {#if message.type === MESSAGE_TYPES.TEXT}
        <div class="prose prose-sm max-w-none {
          message.role === MESSAGE_ROLES.USER 
            ? 'prose-invert' 
            : 'dark:prose-invert'
        }">
          {@html renderMarkdown(message.content)}
        </div>
      {:else if message.type === MESSAGE_TYPES.IMAGE}
        <img 
          src={message.content} 
          alt="Shared image"
          class="max-w-full h-auto rounded-lg shadow-sm"
          style="max-height: 400px;"
        />
      {/if}
      
      <!-- Message footer -->
      <div class="flex items-center justify-between mt-2 pt-2 border-t border-gray-200 dark:border-gray-600">
        <span class="text-xs opacity-70">
          {formatTime(message.timestamp)}
        </span>
        
        <button
          on:click={copyMessage}
          class="p-1 rounded hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
          title="Copy message"
        >
          {#if copied}
            <Check size={14} class="text-green-500" />
          {:else}
            <Copy size={14} class="opacity-70" />
          {/if}
        </button>
      </div>
    </div>
  </div>
</div>

<style>
  :global(.prose code) {
    @apply bg-gray-100 dark:bg-gray-700 px-1 py-0.5 rounded text-sm;
  }
  
  :global(.prose pre) {
    @apply bg-gray-100 dark:bg-gray-800 p-3 rounded-lg overflow-x-auto;
  }
  
  :global(.prose blockquote) {
    @apply border-l-4 border-gray-300 dark:border-gray-600 pl-4 italic;
  }
</style>
