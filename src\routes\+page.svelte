<script>
  import { onMount } from 'svelte';

  // Stores
  import { settings } from '../lib/stores/settings.js';
  import {
    messages,
    addMessage,
    updateMessage,
    isLoading,
    error,
    exportSessions,
    importSessions,
    MESSAGE_ROLES,
    MESSAGE_TYPES
  } from '../lib/stores/messages.js';

  // Services
  import { initializeOpenAI, sendChatMessage, streamChatMessage, analyzeImage } from '../lib/services/openai.js';

  // Components
  import Sidebar from '../lib/components/Sidebar.svelte';
  import ChatMessage from '../lib/components/ChatMessage.svelte';
  import ChatInput from '../lib/components/ChatInput.svelte';
  import SettingsModal from '../lib/components/SettingsModal.svelte';

  // Icons
  import { Menu, X } from 'lucide-svelte';

  let sidebarOpen = true;
  let settingsOpen = false;
  let messagesContainer;
  let fileImportInput;

  // 初始化
  onMount(async () => {
    console.log('Page loaded successfully');

    // 应用暗色模式设置
    if ($settings.darkMode) {
      document.documentElement.classList.add('dark');
    }

    // 初始化OpenAI客户端（使用OpenRouter）
    if ($settings.apiKey) {
      try {
        initializeOpenAI($settings.apiKey, $settings.useOpenRouter);
      } catch (err) {
        console.error('Failed to initialize OpenAI:', err);
      }
    }

    // 滚动到底部
    await scrollToBottom();
  });

  // 滚动到底部
  async function scrollToBottom() {
    if (messagesContainer) {
      setTimeout(() => {
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
      }, 100);
    }
  }

  // 当消息更新时滚动到底部
  $: if ($messages) {
    scrollToBottom();
  }

  // 发送消息
  async function handleSendMessage(event) {
    const { text, files } = event.detail;

    if (!$settings.apiKey) {
      alert('Please configure your OpenAI API key in settings first.');
      settingsOpen = true;
      return;
    }

    try {
      isLoading.set(true);
      error.set(null);

      // 添加用户消息
      const userMessage = addMessage(text, MESSAGE_ROLES.USER, MESSAGE_TYPES.TEXT, { files });

      // 准备消息历史
      const messageHistory = [
        { role: 'system', content: $settings.systemPrompt },
        ...$messages.map(msg => ({
          role: msg.role,
          content: msg.content
        }))
      ];

      // 处理文件附件
      if (files && files.length > 0) {
        for (const file of files) {
          if (file.fileType === 'IMAGE' && file.content?.dataUrl) {
            // 分析图片
            try {
              const imageAnalysis = await analyzeImage(
                file.content.dataUrl,
                text || "What's in this image?",
                { model: $settings.model }
              );

              // 添加图片分析结果
              addMessage(imageAnalysis, MESSAGE_ROLES.ASSISTANT, MESSAGE_TYPES.TEXT);
            } catch (error) {
              console.error('Image analysis error:', error);
              addMessage(`Failed to analyze image: ${error.message}`, MESSAGE_ROLES.ASSISTANT, MESSAGE_TYPES.TEXT);
            }
          } else if (file.fileType === 'PDF' && file.content?.text) {
            // 将PDF内容添加到消息历史
            messageHistory.push({
              role: 'user',
              content: `PDF Content from "${file.name}":\n\n${file.content.text}`
            });
          } else if (file.fileType === 'TEXT' && file.content?.text) {
            // 将文本内容添加到消息历史
            messageHistory.push({
              role: 'user',
              content: `Text file content from "${file.name}":\n\n${file.content.text}`
            });
          }
        }
      }

      // 创建助手消息占位符
      const assistantMessage = addMessage('', MESSAGE_ROLES.ASSISTANT, MESSAGE_TYPES.TEXT);

      // 流式响应
      let fullResponse = '';
      try {
        for await (const chunk of streamChatMessage(messageHistory, {
          model: $settings.model,
          temperature: $settings.temperature,
          maxTokens: $settings.maxTokens
        })) {
          fullResponse += chunk;
          updateMessage(assistantMessage.id, fullResponse);
        }
      } catch (streamError) {
        console.error('Streaming error, falling back to regular chat:', streamError);

        // 回退到常规聊天
        const response = await sendChatMessage(messageHistory, {
          model: $settings.model,
          temperature: $settings.temperature,
          maxTokens: $settings.maxTokens
        });

        updateMessage(assistantMessage.id, response.choices[0].message.content);
      }

    } catch (err) {
      console.error('Chat error:', err);
      error.set(err.message);
      addMessage(`Error: ${err.message}`, MESSAGE_ROLES.ASSISTANT, MESSAGE_TYPES.TEXT);
    } finally {
      isLoading.set(false);
    }
  }

  // 导出会话
  function handleExportSessions() {
    exportSessions();
  }

  // 导入会话
  function handleImportSessions() {
    if (fileImportInput) {
      fileImportInput.click();
    }
  }

  // 处理文件导入
  async function handleFileImport(event) {
    const file = event.target.files?.[0];
    if (file) {
      try {
        await importSessions(file);
        alert('Sessions imported successfully!');
      } catch (error) {
        alert(`Failed to import sessions: ${error.message}`);
      }
    }
    // 清空input
    if (fileImportInput) fileImportInput.value = '';
  }
</script>

<!-- 主应用布局 -->
<div class="h-screen flex bg-gray-50 dark:bg-gray-900">
  <!-- 侧边栏 -->
  <Sidebar
    bind:isOpen={sidebarOpen}
    on:openSettings={() => settingsOpen = true}
    on:exportSessions={handleExportSessions}
    on:importSessions={handleImportSessions}
  />

  <!-- 主内容区域 -->
  <div class="flex-1 flex flex-col min-w-0">
    <!-- 顶部栏 -->
    <header class="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
      <div class="flex items-center gap-3">
        <button
          on:click={() => sidebarOpen = !sidebarOpen}
          class="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors lg:hidden"
        >
          {#if sidebarOpen}
            <X size={20} />
          {:else}
            <Menu size={20} />
          {/if}
        </button>

        <h1 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
          AI Chat Assistant
        </h1>
      </div>

      {#if $error}
        <div class="text-sm text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/20 px-3 py-1 rounded-lg">
          {$error}
        </div>
      {/if}
    </header>

    <!-- 消息区域 -->
    <div
      bind:this={messagesContainer}
      class="flex-1 overflow-y-auto scrollbar-thin"
    >
      {#if $messages.length === 0}
        <!-- 欢迎界面 -->
        <div class="h-full flex items-center justify-center">
          <div class="text-center max-w-md mx-auto p-8">
            <div class="w-16 h-16 bg-primary-100 dark:bg-primary-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-primary-600 dark:text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
              </svg>
            </div>
            <h2 class="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2">
              Welcome to AI Chat Assistant
            </h2>
            <p class="text-gray-600 dark:text-gray-400 mb-6">
              Start a conversation with AI. You can send text messages, upload images for analysis, or share PDF documents for discussion.
            </p>

            {#if !$settings.apiKey}
              <button
                on:click={() => settingsOpen = true}
                class="btn-primary"
              >
                Configure API Key
              </button>
            {:else}
              <p class="text-sm text-gray-500 dark:text-gray-400">
                Type a message below to get started
              </p>
            {/if}
          </div>
        </div>
      {:else}
        <!-- 消息列表 -->
        <div class="py-4">
          {#each $messages as message (message.id)}
            <ChatMessage {message} />
          {/each}

          {#if $isLoading}
            <div class="flex gap-3 p-4">
              <div class="w-8 h-8 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
                <svg class="w-4 h-4 text-gray-600 dark:text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                </svg>
              </div>
              <div class="flex-1 max-w-3xl">
                <div class="message-bubble message-assistant">
                  <div class="flex items-center gap-2 text-gray-500 dark:text-gray-400">
                    <div class="flex space-x-1">
                      <div class="w-2 h-2 bg-current rounded-full animate-bounce"></div>
                      <div class="w-2 h-2 bg-current rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
                      <div class="w-2 h-2 bg-current rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
                    </div>
                    <span class="text-sm">AI is thinking...</span>
                  </div>
                </div>
              </div>
            </div>
          {/if}
        </div>
      {/if}
    </div>

    <!-- 输入区域 -->
    <ChatInput
      disabled={$isLoading || !$settings.apiKey}
      placeholder={$settings.apiKey ? "Type your message..." : "Please configure your API key in settings first"}
      on:send={handleSendMessage}
    />
  </div>
</div>

<!-- 设置模态框 -->
<SettingsModal
  bind:isOpen={settingsOpen}
  on:close={() => settingsOpen = false}
/>
