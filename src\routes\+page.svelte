<script>
  import { onMount } from 'svelte';

  // Stores
  import { settings } from '../lib/stores/settings.js';
  import {
    messages,
    addMessage,
    updateMessage,
    isLoading,
    error,
    exportSessions,
    importSessions,
    MESSAGE_ROLES,
    MESSAGE_TYPES
  } from '../lib/stores/messages.js';

  // Services
  import { initializeOpenAI, sendChatMessage, streamChatMessage, analyzeImage } from '../lib/services/openai.js';

  // Components
  import SettingsModal from '../lib/components/SettingsModal.svelte';

  let sidebarOpen = true;
  let settingsOpen = false;
  let messagesContainer;
  let fileImportInput;

  // 初始化
  onMount(() => {
    console.log('Page loaded successfully');

    // 应用暗色模式设置
    if ($settings.darkMode) {
      document.documentElement.classList.add('dark');
    }

    // 初始化OpenAI客户端
    if ($settings.apiKey) {
      try {
        initializeOpenAI($settings.apiKey);
      } catch (err) {
        console.error('Failed to initialize OpenAI:', err);
      }
    }
  });
</script>

<div class="h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
  <div class="text-center max-w-md mx-auto p-8">
    <div class="w-16 h-16 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
      <svg class="w-8 h-8 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
      </svg>
    </div>
    <h1 class="text-4xl font-bold text-gray-900 dark:text-gray-100 mb-4">
      AI Chat Assistant
    </h1>
    <p class="text-lg text-gray-600 dark:text-gray-400 mb-8">
      Welcome to your AI-powered chat assistant
    </p>
    <div class="space-y-4">
      <div class="p-4 bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700">
        <p class="text-sm text-gray-500 dark:text-gray-400">
          {#if $settings.apiKey}
            ✅ API Key configured
          {:else}
            ⚠️ Please configure your OpenAI API key to get started
          {/if}
        </p>
        <p class="text-xs text-gray-400 dark:text-gray-500 mt-2">
          Current model: {$settings.model || 'gpt-4o'}
        </p>
      </div>

      {#if !$settings.apiKey}
        <button
          class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          on:click={() => settingsOpen = true}
        >
          Configure API Key
        </button>
      {:else}
        <div class="space-y-2">
          <p class="text-sm text-gray-500 dark:text-gray-400">
            Ready to chat! The full interface is being loaded...
          </p>
          <button
            class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors text-sm"
            on:click={() => settingsOpen = true}
          >
            Settings
          </button>
        </div>
      {/if}
    </div>
  </div>
</div>

<!-- 设置模态框 -->
<SettingsModal
  bind:isOpen={settingsOpen}
  on:close={() => settingsOpen = false}
/>
