<script>
  import { onMount } from 'svelte';

  // Stores
  import { settings } from '../lib/stores/settings.js';
  import {
    messages,
    addMessage,
    updateMessage,
    isLoading,
    error,
    exportSessions,
    importSessions,
    MESSAGE_ROLES,
    MESSAGE_TYPES
  } from '../lib/stores/messages.js';

  // Services
  import { initializeOpenAI, sendChatMessage, streamChatMessage, analyzeImage } from '../lib/services/openai.js';

  // Components
  import Sidebar from '../lib/components/Sidebar.svelte';
  import ChatMessage from '../lib/components/ChatMessage.svelte';
  import ChatInput from '../lib/components/ChatInput.svelte';
  import SettingsModal from '../lib/components/SettingsModal.svelte';

  // Icons
  import { Menu, X } from 'lucide-svelte';

  let sidebarOpen = true;
  let settingsOpen = false;
  let messagesContainer;
  let fileImportInput;

  // 初始化
  onMount(async () => {
    console.log('Page loaded successfully');

    // 应用暗色模式设置
    if ($settings.darkMode) {
      document.documentElement.classList.add('dark');
    }

    // 初始化OpenAI客户端（使用OpenRouter）
    if ($settings.apiKey) {
      try {
        initializeOpenAI($settings.apiKey, $settings.useOpenRouter);
      } catch (err) {
        console.error('Failed to initialize OpenAI:', err);
      }
    }

    // 滚动到底部
    await scrollToBottom();
  });

  // 滚动到底部
  async function scrollToBottom() {
    if (messagesContainer) {
      setTimeout(() => {
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
      }, 100);
    }
  }

  // 当消息更新时滚动到底部
  $: if ($messages) {
    scrollToBottom();
  }

  // 发送消息
  async function handleSendMessage(event) {
    console.log('handleSendMessage called with:', event.detail);
    const { text, files } = event.detail;

    if (!text || text.trim() === '') {
      console.log('Empty message, ignoring');
      return;
    }

    if (!$settings.apiKey) {
      alert('Please configure your OpenAI API key in settings first.');
      settingsOpen = true;
      return;
    }

    // 测试模式：如果API密钥是"test"，返回模拟响应
    if ($settings.apiKey === 'test') {
      console.log('Test mode activated');
      const assistantMessage = addMessage('This is a test response. Your message was: "' + text + '"', MESSAGE_ROLES.ASSISTANT, MESSAGE_TYPES.TEXT);
      return;
    }

    console.log('Starting message send process...');
    console.log('Settings:', $settings);

    try {
      isLoading.set(true);
      error.set(null);

      // 添加用户消息
      console.log('Adding user message...');
      const userMessage = addMessage(text, MESSAGE_ROLES.USER, MESSAGE_TYPES.TEXT, { files });
      console.log('User message added:', userMessage);

      // 确保OpenAI客户端已初始化
      try {
        console.log('Initializing OpenAI client...');
        initializeOpenAI($settings.apiKey, $settings.useOpenRouter);
        console.log('OpenAI client initialized successfully');
      } catch (initError) {
        console.error('Failed to initialize OpenAI client:', initError);
        const errorMsg = initError instanceof Error ? initError.message : 'Unknown initialization error';
        throw new Error(`Failed to initialize AI client: ${errorMsg}`);
      }

      // 准备消息历史 - 简化版本
      console.log('Building message history...');
      console.log('Current messages:', $messages);

      // 只包含最近的几条消息以避免token限制
      const recentMessages = $messages.slice(-5); // 只取最近5条消息
      const messageHistory = [
        { role: 'system', content: $settings.systemPrompt || 'You are a helpful AI assistant.' },
        ...recentMessages.map(msg => ({
          role: msg.role,
          content: msg.content
        })),
        { role: 'user', content: text } // 添加当前用户消息
      ];
      console.log('Message history built:', messageHistory);

      // 暂时跳过文件处理以简化调试
      if (files && files.length > 0) {
        console.log('Files detected but skipping for now:', files);
        addMessage('File processing is temporarily disabled for debugging.', MESSAGE_ROLES.ASSISTANT, MESSAGE_TYPES.TEXT);
        return;
      }

      // 创建助手消息占位符
      console.log('Creating assistant message placeholder...');
      const assistantMessage = addMessage('正在思考...', MESSAGE_ROLES.ASSISTANT, MESSAGE_TYPES.TEXT);
      console.log('Assistant message created:', assistantMessage);

      // 简化的API调用
      console.log('Starting API call...');
      console.log('API parameters:', {
        model: $settings.model || 'openai/gpt-4o',
        temperature: $settings.temperature || 0.7,
        maxTokens: $settings.maxTokens || 2000
      });

      try {
        console.log('Calling sendChatMessage...');
        const response = await sendChatMessage(messageHistory, {
          model: $settings.model || 'openai/gpt-4o',
          temperature: $settings.temperature || 0.7,
          maxTokens: $settings.maxTokens || 2000
        });

        console.log('API response received:', response);

        if (response && response.choices && response.choices[0] && response.choices[0].message) {
          const content = response.choices[0].message.content;
          console.log('Updating message with content:', content);
          updateMessage(assistantMessage.id, content);
          console.log('Message updated successfully');
        } else {
          console.error('Invalid response format:', response);
          updateMessage(assistantMessage.id, 'Sorry, I received an invalid response from the AI service.');
        }
      } catch (apiError) {
        console.error('API call failed:', apiError);
        const errorMessage = apiError instanceof Error ? apiError.message : 'Unknown API error';
        updateMessage(assistantMessage.id, `Error: ${errorMessage}`);
        throw apiError;
      }

    } catch (err) {
      console.error('Chat error:', err);
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      error.set(errorMessage);
      addMessage(`Error: ${errorMessage}`, MESSAGE_ROLES.ASSISTANT, MESSAGE_TYPES.TEXT);
    } finally {
      isLoading.set(false);
    }
  }

  // 导出会话
  function handleExportSessions() {
    exportSessions();
  }

  // 导入会话
  function handleImportSessions() {
    if (fileImportInput) {
      fileImportInput.click();
    }
  }

  // 处理文件导入
  async function handleFileImport(event) {
    const file = event.target.files?.[0];
    if (file) {
      try {
        await importSessions(file);
        alert('Sessions imported successfully!');
      } catch (error) {
        alert(`Failed to import sessions: ${error.message}`);
      }
    }
    // 清空input
    if (fileImportInput) fileImportInput.value = '';
  }
</script>

<!-- 主应用布局 -->
<div class="h-screen flex bg-gray-50 dark:bg-gray-900">
  <!-- 侧边栏 -->
  <Sidebar
    bind:isOpen={sidebarOpen}
    on:openSettings={() => settingsOpen = true}
    on:exportSessions={handleExportSessions}
    on:importSessions={handleImportSessions}
  />

  <!-- 主内容区域 -->
  <div class="flex-1 flex flex-col min-w-0">
    <!-- 顶部栏 -->
    <header class="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
      <div class="flex items-center gap-3">
        <button
          on:click={() => sidebarOpen = !sidebarOpen}
          class="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors lg:hidden"
        >
          {#if sidebarOpen}
            <X size={20} />
          {:else}
            <Menu size={20} />
          {/if}
        </button>

        <h1 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
          AI Chat Assistant
        </h1>
      </div>

      {#if $error}
        <div class="text-sm text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/20 px-3 py-1 rounded-lg">
          {$error}
        </div>
      {/if}
    </header>

    <!-- 消息区域 -->
    <div
      bind:this={messagesContainer}
      class="flex-1 overflow-y-auto scrollbar-thin"
    >
      {#if $messages.length === 0}
        <!-- 欢迎界面 -->
        <div class="h-full flex items-center justify-center">
          <div class="text-center max-w-md mx-auto p-8">
            <div class="w-16 h-16 bg-primary-100 dark:bg-primary-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-primary-600 dark:text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
              </svg>
            </div>
            <h2 class="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2">
              Welcome to AI Chat Assistant
            </h2>
            <p class="text-gray-600 dark:text-gray-400 mb-6">
              Start a conversation with AI. You can send text messages, upload images for analysis, or share PDF documents for discussion.
            </p>

            {#if !$settings.apiKey}
              <button
                on:click={() => settingsOpen = true}
                class="btn-primary"
              >
                Configure API Key
              </button>
            {:else}
              <p class="text-sm text-gray-500 dark:text-gray-400">
                Type a message below to get started
              </p>
            {/if}
          </div>
        </div>
      {:else}
        <!-- 消息列表 -->
        <div class="py-4">
          {#each $messages as message (message.id)}
            <ChatMessage {message} />
          {/each}

          {#if $isLoading}
            <div class="flex gap-3 p-4">
              <div class="w-8 h-8 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
                <svg class="w-4 h-4 text-gray-600 dark:text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                </svg>
              </div>
              <div class="flex-1 max-w-3xl">
                <div class="message-bubble message-assistant">
                  <div class="flex items-center gap-2 text-gray-500 dark:text-gray-400">
                    <div class="flex space-x-1">
                      <div class="w-2 h-2 bg-current rounded-full animate-bounce"></div>
                      <div class="w-2 h-2 bg-current rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
                      <div class="w-2 h-2 bg-current rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
                    </div>
                    <span class="text-sm">AI is thinking...</span>
                  </div>
                </div>
              </div>
            </div>
          {/if}
        </div>
      {/if}
    </div>

    <!-- 输入区域 -->
    <ChatInput
      disabled={$isLoading || !$settings.apiKey}
      placeholder={$settings.apiKey ? "Type your message..." : "Please configure your API key in settings first"}
      on:send={handleSendMessage}
    />
  </div>
</div>

<!-- 设置模态框 -->
<SettingsModal
  bind:isOpen={settingsOpen}
  on:close={() => settingsOpen = false}
/>

<!-- 隐藏的文件导入输入 -->
<input
  bind:this={fileImportInput}
  type="file"
  accept=".json"
  on:change={handleFileImport}
  class="hidden"
/>
